import { spawn } from 'child_process';
import fs from 'fs-extra';
import path from 'path';
import { fileURLToPath } from 'url';
import moment from 'moment';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const rootDir = path.join(__dirname, '..');

export class TestRunner {
  constructor() {
    this.resultsDir = path.join(rootDir, 'results');
    this.k6Dir = path.join(rootDir, 'k6');
    this.ensureDirectories();
  }

  async ensureDirectories() {
    await fs.ensureDir(this.resultsDir);
    await fs.ensureDir(this.k6Dir);
  }

  async checkK6() {
    return new Promise((resolve) => {
      const child = spawn('k6', ['version'], { stdio: 'pipe' });
      child.on('close', (code) => {
        resolve(code === 0);
      });
      child.on('error', () => {
        resolve(false);
      });
    });
  }

  async runTest(options = {}) {
    const {
      url = 'https://httpbin.org',
      type = 'load',
      duration,
      vus,
      config,
      noThresholds = false
    } = options;

    // Tạo timestamp cho file kết quả
    const timestamp = moment().format('YYYYMMDD_HHmmss');
    const logFile = path.join(this.resultsDir, `test_${type}_${timestamp}.json`);
    const consoleFile = path.join(this.resultsDir, `console_${type}_${timestamp}.txt`);
    const summaryFile = path.join(this.resultsDir, `summary_${timestamp}.json`);

    // Tạo K6 script động
    const scriptPath = await this.generateK6Script(type, config, noThresholds);

    // Tạo K6 command
    const k6Args = [
      'run',
      '--env', `BASE_URL=${url}`,
      '--console-output', consoleFile,
      '--out', `json=${logFile}`,
    ];

    // Thêm options tùy chỉnh
    if (duration) {
      k6Args.push('--duration', duration);
    }
    if (vus) {
      k6Args.push('--vus', vus);
    }

    k6Args.push(scriptPath);

    // Chạy K6
    await this.executeK6(k6Args);

    // Tạo summary file
    await this.createSummary(logFile, summaryFile);

    return {
      logFile,
      consoleFile,
      summaryFile,
      timestamp
    };
  }

  async generateK6Script(testType, configFile, noThresholds = false) {
    const scriptPath = path.join(this.k6Dir, `test-${testType}.js`);

    // Load config
    let config = this.getDefaultConfig(testType);
    if (configFile && await fs.pathExists(configFile)) {
      const userConfig = await import(path.resolve(configFile));
      config = { ...config, ...userConfig.default };
    }

    // Remove thresholds if requested
    if (noThresholds) {
      config.thresholds = {};
    }

    const scriptContent = this.generateScriptContent(config);
    await fs.writeFile(scriptPath, scriptContent);

    return scriptPath;
  }

  getDefaultConfig(testType) {
    const configs = {
      smoke: {
        stages: [{ duration: '1m', target: 1 }],
        thresholds: {
          http_req_duration: ['p(95)<5000'],  // 5 giây - rất dễ dàng
          http_req_failed: ['rate<0.5'],      // 50% error rate - rất dễ dàng
        }
      },
      load: {
        stages: [
          { duration: '2m', target: 10 },
          { duration: '5m', target: 10 },
          { duration: '2m', target: 0 },
        ],
        thresholds: {
          http_req_duration: ['p(95)<2000'],  // 2 giây
          http_req_failed: ['rate<0.2'],      // 20% error rate
        }
      },
      stress: {
        stages: [
          { duration: '2m', target: 10 },
          { duration: '5m', target: 20 },
          { duration: '2m', target: 30 },
          { duration: '5m', target: 30 },
          { duration: '2m', target: 0 },
        ],
        thresholds: {
          http_req_duration: ['p(95)<3000'],  // 3 giây
          http_req_failed: ['rate<0.3'],      // 30% error rate
        }
      },
      spike: {
        stages: [
          { duration: '1m', target: 10 },
          { duration: '30s', target: 50 },
          { duration: '1m', target: 10 },
          { duration: '30s', target: 0 },
        ],
        thresholds: {
          http_req_duration: ['p(95)<5000'],  // 5 giây
          http_req_failed: ['rate<0.4'],      // 40% error rate
        }
      }
    };

    return configs[testType] || configs.load;
  }

  generateScriptContent(config) {
    return `import http from 'k6/http';
import { check, sleep } from 'k6';
import { SharedArray } from 'k6/data';
import { textSummary } from 'https://jslib.k6.io/k6-summary/0.0.1/index.js';

export const options = ${JSON.stringify({
  stages: config.stages,
  thresholds: config.thresholds
}, null, 2)};

const BASE_URL = __ENV.BASE_URL || 'https://httpbin.org';

const endpoints = new SharedArray('endpoints', function () {
  return [
    '/json',
    '/get',
    '/uuid',
    '/delay/1',
  ];
});

function generateRandomQuery() {
  const timestamp = Date.now();
  const random = Math.random().toString(36).substring(7);
  return \`t=\${timestamp}&r=\${random}\`;
}

function logRequestDetails(response, url, startTime) {
  const endTime = Date.now();
  const responseTime = endTime - startTime;
  
  const logEntry = {
    timestamp: new Date().toISOString(),
    url: url,
    method: 'GET',
    status_code: response.status,
    response_time_ms: responseTime,
    response_size_bytes: response.body ? response.body.length : 0,
    success: response.status >= 200 && response.status < 300,
    error: response.status >= 400 ? response.status_text : null,
    user_id: __VU,
    iteration: __ITER,
  };

  try {
    if (response.headers['Content-Type'] && 
        response.headers['Content-Type'].includes('application/json')) {
      const jsonData = JSON.parse(response.body);
      logEntry.response_data = {
        has_data: jsonData.hasOwnProperty('data'),
        has_status: jsonData.hasOwnProperty('status'),
        data_type: typeof jsonData.data,
        custom_fields: {
          user_id: jsonData.user_id || null,
          message: jsonData.message || null,
          result_count: Array.isArray(jsonData.data) ? jsonData.data.length : null,
        }
      };
    }
  } catch (e) {
    logEntry.json_parse_error = e.message;
  }

  console.log(JSON.stringify(logEntry));
  return logEntry;
}

export default function () {
  const startTime = Date.now();
  
  const endpoint = endpoints[Math.floor(Math.random() * endpoints.length)];
  const queryParams = generateRandomQuery();
  const url = \`\${BASE_URL}\${endpoint}?\${queryParams}\`;
  
  const params = {
    headers: {
      'User-Agent': 'K6-LoadTest-NodeJS/1.0',
      'Accept': 'application/json',
      'Cache-Control': 'no-cache',
    },
    timeout: '30s',
  };

  const response = http.get(url, params);
  
  logRequestDetails(response, url, startTime);
  
  check(response, {
    'status is 200': (r) => r.status === 200,
    'response time < 500ms': (r) => r.timings.duration < 500,
    'response has body': (r) => r.body && r.body.length > 0,
    'content-type is JSON': (r) => 
      r.headers['Content-Type'] && 
      r.headers['Content-Type'].includes('application/json'),
  });

  if (response.status === 200 && response.body) {
    try {
      const jsonData = JSON.parse(response.body);
      
      check(jsonData, {
        'has status field': (data) => data.hasOwnProperty('status') || data.hasOwnProperty('url'),
        'has data field': (data) => data.hasOwnProperty('data') || data.hasOwnProperty('origin'),
        'data is not empty': (data) => {
          if (data.data) return data.data !== null && data.data !== '';
          return true;
        },
      });
    } catch (e) {
      console.log(\`JSON parse error: \${e.message}\`);
    }
  }

  sleep(Math.random() * 2 + 1);
}

export function handleSummary(data) {
  const summary = textSummary(data, { indent: ' ', enableColors: true });
  
  const detailedReport = {
    test_info: {
      start_time: new Date(data.state.testRunDurationMs).toISOString(),
      duration_ms: data.state.testRunDurationMs,
      vus_max: data.metrics.vus_max.values.max,
      iterations: data.metrics.iterations.values.count,
    },
    metrics: {
      http_req_duration: {
        avg: data.metrics.http_req_duration.values.avg,
        min: data.metrics.http_req_duration.values.min,
        max: data.metrics.http_req_duration.values.max,
        p90: data.metrics.http_req_duration.values['p(90)'],
        p95: data.metrics.http_req_duration.values['p(95)'],
        p99: data.metrics.http_req_duration.values['p(99)'],
      },
      http_req_failed: {
        rate: data.metrics.http_req_failed.values.rate,
        count: data.metrics.http_req_failed.values.count,
      },
      http_reqs: {
        count: data.metrics.http_reqs.values.count,
        rate: data.metrics.http_reqs.values.rate,
      },
      checks: {
        rate: data.metrics.checks.values.rate,
        passes: data.metrics.checks.values.passes,
        fails: data.metrics.checks.values.fails,
      }
    },
    thresholds: data.thresholds,
  };

  return {
    'stdout': summary,
    'summary.json': JSON.stringify(detailedReport, null, 2),
    'detailed-results.json': JSON.stringify(data, null, 2),
  };
}`;
  }

  async executeK6(args) {
    return new Promise((resolve, reject) => {
      const child = spawn('k6', args, { 
        stdio: ['inherit', 'inherit', 'inherit'],
        cwd: rootDir
      });

      child.on('close', (code) => {
        if (code === 0) {
          resolve();
        } else {
          reject(new Error(`K6 exited with code ${code}`));
        }
      });

      child.on('error', (error) => {
        reject(error);
      });
    });
  }

  async createSummary(logFile, summaryFile) {
    try {
      // Đọc summary.json nếu có
      const summaryPath = path.join(rootDir, 'summary.json');
      if (await fs.pathExists(summaryPath)) {
        const summaryData = await fs.readJson(summaryPath);
        await fs.writeJson(summaryFile, summaryData, { spaces: 2 });
        
        // Di chuyển file summary.json vào results
        await fs.move(summaryPath, summaryFile.replace('.json', '_k6.json'));
      }
    } catch (error) {
      console.warn('Không thể tạo summary file:', error.message);
    }
  }
}
