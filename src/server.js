import express from 'express';
import cors from 'cors';
import multer from 'multer';
import compression from 'compression';
import helmet from 'helmet';
import fs from 'fs-extra';
import path from 'path';
import { fileURLToPath } from 'url';
import chalk from 'chalk';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const rootDir = path.join(__dirname, '..');

const app = express();
const PORT = process.env.PORT || 3000;

// Middleware
app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'", "https://cdn.jsdelivr.net"],
      scriptSrc: ["'self'", "'unsafe-inline'", "https://cdn.jsdelivr.net"],
      imgSrc: ["'self'", "data:", "https:"],
    },
  },
}));
app.use(compression());
app.use(cors());
app.use(express.json({ limit: '50mb' }));
app.use(express.urlencoded({ extended: true, limit: '50mb' }));

// Serve static files
app.use(express.static(path.join(rootDir, 'public')));

// Configure multer for file uploads
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    const uploadDir = path.join(rootDir, 'results', 'uploads');
    fs.ensureDirSync(uploadDir);
    cb(null, uploadDir);
  },
  filename: (req, file, cb) => {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    cb(null, `upload_${timestamp}_${file.originalname}`);
  }
});

const upload = multer({
  storage,
  limits: { fileSize: 100 * 1024 * 1024 }, // 100MB
  fileFilter: (req, file, cb) => {
    const allowedTypes = ['application/json', 'text/plain'];
    const allowedExtensions = ['.json', '.txt'];

    const isValidMimeType = allowedTypes.includes(file.mimetype);
    const isValidExtension = allowedExtensions.some(ext => file.originalname.endsWith(ext));

    if (isValidMimeType || isValidExtension) {
      cb(null, true);
    } else {
      cb(new Error('Chỉ chấp nhận file JSON hoặc TXT (K6 output)!'), false);
    }
  }
});

// API Routes

// Get all test results
app.get('/api/results', async (req, res) => {
  try {
    const resultsDir = path.join(rootDir, 'results');
    await fs.ensureDir(resultsDir);
    
    const files = await fs.readdir(resultsDir);
    const jsonFiles = files.filter(file => file.endsWith('.json'));
    
    const results = await Promise.all(
      jsonFiles.map(async (file) => {
        const filePath = path.join(resultsDir, file);
        const stats = await fs.stat(filePath);
        
        return {
          filename: file,
          path: filePath,
          size: stats.size,
          created: stats.birthtime,
          modified: stats.mtime,
          type: file.includes('summary') ? 'summary' : 
                file.includes('console') ? 'console' : 'raw'
        };
      })
    );
    
    // Sort by creation time (newest first)
    results.sort((a, b) => new Date(b.created) - new Date(a.created));
    
    res.json(results);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Get specific result file
app.get('/api/results/:filename', async (req, res) => {
  try {
    const filename = req.params.filename;
    const filePath = path.join(rootDir, 'results', filename);
    
    if (!await fs.pathExists(filePath)) {
      return res.status(404).json({ error: 'File không tồn tại' });
    }
    
    const data = await fs.readJson(filePath);
    res.json(data);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Upload and analyze file
app.post('/api/upload', upload.single('file'), async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({ error: 'Không có file được upload' });
    }

    const filePath = req.file.path;

    // Read file as text first to handle different formats
    const fileContent = await fs.readFile(filePath, 'utf8');
    let data;

    try {
      // Try to parse as JSON
      data = JSON.parse(fileContent);
    } catch (jsonError) {
      // If JSON parsing fails, try to handle K6 NDJSON format (newline-delimited JSON)
      try {
        const lines = fileContent.split('\n').filter(line => line.trim());
        data = [];

        for (const line of lines) {
          if (line.trim()) {
            try {
              const parsed = JSON.parse(line);
              // Check if it's K6 data format
              if (parsed.type === 'Point' && parsed.data) {
                data.push(parsed.data);
              } else if (parsed.timestamp && parsed.response_time_ms !== undefined) {
                // Direct log format
                data.push(parsed);
              } else if (parsed.metrics) {
                // Summary format
                data = parsed;
                break;
              }
            } catch (lineError) {
              // Skip invalid lines
              continue;
            }
          }
        }

        if (data.length === 0 && typeof data !== 'object') {
          throw new Error('Không tìm thấy dữ liệu hợp lệ trong file');
        }
      } catch (ndjsonError) {
        await fs.remove(filePath);
        return res.status(400).json({
          error: 'File JSON không hợp lệ. Vui lòng kiểm tra định dạng file.',
          details: `JSON Error: ${jsonError.message}`,
          suggestion: 'File phải là JSON hợp lệ hoặc K6 NDJSON output'
        });
      }
    }

    // Basic validation
    if (!data || (typeof data !== 'object' && !Array.isArray(data))) {
      await fs.remove(filePath);
      return res.status(400).json({ error: 'File JSON không chứa dữ liệu hợp lệ' });
    }

    // Log successful upload
    console.log(`✅ Upload successful: ${req.file.originalname} (${req.file.size} bytes)`);

    res.json({
      message: 'Upload thành công',
      filename: req.file.filename,
      originalName: req.file.originalname,
      size: req.file.size,
      dataType: Array.isArray(data) ? 'array' : 'object',
      recordCount: Array.isArray(data) ? data.length : (data.metrics ? 'summary' : 'unknown'),
      data: data
    });
  } catch (error) {
    console.error('❌ Upload error:', error.message);
    if (req.file) {
      await fs.remove(req.file.path);
    }
    res.status(500).json({
      error: 'Lỗi xử lý file',
      details: error.message,
      suggestion: 'Vui lòng kiểm tra định dạng file và thử lại'
    });
  }
});

// Delete result file
app.delete('/api/results/:filename', async (req, res) => {
  try {
    const filename = req.params.filename;
    const filePath = path.join(rootDir, 'results', filename);
    
    if (!await fs.pathExists(filePath)) {
      return res.status(404).json({ error: 'File không tồn tại' });
    }
    
    await fs.remove(filePath);
    res.json({ message: 'Đã xóa file thành công' });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Get system info
app.get('/api/system', async (req, res) => {
  try {
    const resultsDir = path.join(rootDir, 'results');
    const files = await fs.readdir(resultsDir);
    const totalFiles = files.filter(f => f.endsWith('.json')).length;
    
    // Calculate total size
    let totalSize = 0;
    for (const file of files) {
      const stats = await fs.stat(path.join(resultsDir, file));
      totalSize += stats.size;
    }
    
    res.json({
      totalFiles,
      totalSize,
      resultsDir,
      nodeVersion: process.version,
      platform: process.platform,
      uptime: process.uptime()
    });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Analyze data endpoint
app.post('/api/analyze', async (req, res) => {
  try {
    const { data } = req.body;
    
    if (!data || !Array.isArray(data)) {
      return res.status(400).json({ error: 'Dữ liệu không hợp lệ' });
    }
    
    // Perform analysis
    const analysis = analyzeData(data);
    
    res.json(analysis);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Helper function to analyze data
function analyzeData(data) {
  if (data.length === 0) return {};
  
  const responseTimes = data.map(d => d.response_time_ms || 0);
  const successCount = data.filter(d => 
    d.success || (d.status_code >= 200 && d.status_code < 300)
  ).length;
  
  // Calculate percentiles
  const sortedTimes = responseTimes.sort((a, b) => a - b);
  const p50 = sortedTimes[Math.floor(sortedTimes.length * 0.5)];
  const p90 = sortedTimes[Math.floor(sortedTimes.length * 0.9)];
  const p95 = sortedTimes[Math.floor(sortedTimes.length * 0.95)];
  const p99 = sortedTimes[Math.floor(sortedTimes.length * 0.99)];
  
  // Calculate RPS
  const timestamps = data.map(d => new Date(d.timestamp).getTime());
  const duration = (Math.max(...timestamps) - Math.min(...timestamps)) / 1000;
  const rps = duration > 0 ? Math.round(data.length / duration) : 0;
  
  // Status code distribution
  const statusCodes = {};
  data.forEach(d => {
    const status = d.status_code || 'Unknown';
    statusCodes[status] = (statusCodes[status] || 0) + 1;
  });
  
  return {
    totalRequests: data.length,
    successCount,
    errorCount: data.length - successCount,
    successRate: Math.round((successCount / data.length) * 100),
    errorRate: Math.round(((data.length - successCount) / data.length) * 100),
    responseTime: {
      avg: Math.round(responseTimes.reduce((a, b) => a + b, 0) / responseTimes.length),
      min: Math.min(...responseTimes),
      max: Math.max(...responseTimes),
      p50: Math.round(p50),
      p90: Math.round(p90),
      p95: Math.round(p95),
      p99: Math.round(p99)
    },
    rps,
    statusCodes,
    duration: Math.round(duration),
    timeRange: {
      start: new Date(Math.min(...timestamps)).toISOString(),
      end: new Date(Math.max(...timestamps)).toISOString()
    }
  };
}

// Serve main UI
app.get('/', (req, res) => {
  res.sendFile(path.join(rootDir, 'public', 'index.html'));
});

// Error handling middleware
app.use((error, req, res, next) => {
  console.error(chalk.red('Server Error:'), error);
  
  if (error instanceof multer.MulterError) {
    if (error.code === 'LIMIT_FILE_SIZE') {
      return res.status(400).json({ error: 'File quá lớn (tối đa 100MB)' });
    }
  }
  
  res.status(500).json({ error: 'Lỗi server nội bộ' });
});

// 404 handler
app.use((req, res) => {
  res.status(404).json({ error: 'Endpoint không tồn tại' });
});

// Start server
const server = app.listen(PORT, () => {
  console.log(chalk.green(`🚀 Server đang chạy tại: http://localhost:${PORT}`));
  console.log(chalk.blue(`📊 UI: http://localhost:${PORT}`));
  console.log(chalk.yellow(`📁 Results: ${path.join(rootDir, 'results')}`));
});

// Graceful shutdown
process.on('SIGTERM', () => {
  console.log(chalk.yellow('\n🛑 Đang tắt server...'));
  server.close(() => {
    console.log(chalk.green('✅ Server đã tắt.'));
    process.exit(0);
  });
});

export default app;
