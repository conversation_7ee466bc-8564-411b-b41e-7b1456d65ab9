#!/bin/bash

# Demo script để test hệ thống load testing
# Chạy một test nhanh và mở UI để xem kết quả

set -e

# Màu sắc
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

echo -e "${BLUE}🚀 K6 Load Testing Demo${NC}"
echo "=================================="

# Kiểm tra K6
if ! command -v k6 &> /dev/null; then
    echo -e "${RED}❌ K6 chưa được cài đặt!${NC}"
    echo "Vui lòng cài đặt K6 trước:"
    echo "  macOS: brew install k6"
    echo "  Ubuntu: sudo apt install k6"
    echo "  Windows: choco install k6"
    exit 1
fi

echo -e "${GREEN}✅ K6 đã được cài đặt${NC}"

# Tạo thư mục results
mkdir -p results

# Chạy demo test với httpbin.org
echo -e "${YELLOW}📡 Chạy demo test với httpbin.org...${NC}"
echo "Thời gian: ~2 phút"
echo ""

# Tạo timestamp
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
LOG_FILE="results/demo_${TIMESTAMP}.json"

# Chạy test ngắn
k6 run \
    --env BASE_URL="https://httpbin.org" \
    --console-output="results/demo_console_${TIMESTAMP}.txt" \
    --out json="$LOG_FILE" \
    --duration 1m \
    --vus 5 \
    load-test.js

if [ $? -eq 0 ]; then
    echo -e "${GREEN}✅ Demo test hoàn thành!${NC}"
    echo ""
    
    # Hiển thị quick stats
    if [ -f "summary.json" ]; then
        echo -e "${GREEN}📊 Kết quả nhanh:${NC}"
        if command -v jq &> /dev/null; then
            echo -n "  • Total requests: "
            jq -r '.metrics.http_reqs.count // "N/A"' summary.json
            echo -n "  • Average response time: "
            jq -r '(.metrics.http_req_duration.avg // 0) | (. * 1000 | floor) / 1000 | tostring + "ms"' summary.json
            echo -n "  • Error rate: "
            jq -r '(.metrics.http_req_failed.rate // 0) | (. * 100 | floor) / 100 | tostring + "%"' summary.json
        else
            echo "  (Cài đặt 'jq' để xem stats chi tiết)"
        fi
        echo ""
    fi
    
    echo -e "${GREEN}📁 Files được tạo:${NC}"
    echo "  • $LOG_FILE"
    echo "  • summary.json"
    echo "  • detailed-results.json"
    echo ""
    
    # Hướng dẫn xem UI
    echo -e "${BLUE}🌐 Để xem kết quả trong UI:${NC}"
    echo "1. Mở file index.html trong trình duyệt"
    echo "2. Kéo thả file '$LOG_FILE' vào trang"
    echo "3. Khám phá các biểu đồ và bộ lọc"
    echo ""
    
    # Tự động mở browser nếu có thể
    if command -v open &> /dev/null; then
        echo -e "${YELLOW}🔄 Đang mở UI trong trình duyệt...${NC}"
        open index.html
    elif command -v xdg-open &> /dev/null; then
        echo -e "${YELLOW}🔄 Đang mở UI trong trình duyệt...${NC}"
        xdg-open index.html
    else
        echo -e "${YELLOW}💡 Mở file index.html trong trình duyệt để xem kết quả${NC}"
    fi
    
    echo ""
    echo -e "${GREEN}🎉 Demo hoàn thành! Chúc bạn load testing vui vẻ!${NC}"
    
else
    echo -e "${RED}❌ Demo test thất bại!${NC}"
    echo "Kiểm tra log để biết thêm chi tiết."
    exit 1
fi
