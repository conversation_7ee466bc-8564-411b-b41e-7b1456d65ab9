// K6 Load Test Configuration
// Generated at 2025-06-24T17:54:25.593Z

export const testConfig = {
  // URL cơ bản để test
  baseUrl: 'https://dev05.xi88.com',
  
  // Danh sách endpoints để test
  endpoints: [
    "/"
],
  
  // Loại test mặc định
  defaultTestType: 'spike',
  
  // Cấu hình stages cho từng loại test
  stages: {
    smoke: [
      { duration: '1m', target: 1 },
    ],
    load: [
      { duration: '2m', target: 10 },
      { duration: '5m', target: 10 },
      { duration: '2m', target: 0 },
    ],
    stress: [
      { duration: '2m', target: 10 },
      { duration: '5m', target: 20 },
      { duration: '2m', target: 30 },
      { duration: '5m', target: 30 },
      { duration: '2m', target: 0 },
    ],
    spike: [
      { duration: '1m', target: 10 },
      { duration: '30s', target: 50 },
      { duration: '1m', target: 10 },
      { duration: '30s', target: 0 },
    ]
  },
  
  // Ngưỡng chấp nhận (thresholds)
  thresholds: {
    http_req_duration: ['p(95)<10000'],
    http_req_failed: ['rate<0.5'],
    checks: ['rate>0.95'],
  },
  
  // Headers mặc định
  defaultHeaders: {
    'User-Agent': 'K6-LoadTest-NodeJS/1.0',
    'Accept': 'application/json',
    'Cache-Control': 'no-cache',
  },
  
  // Cấu hình authentication
  auth: {},
  
  // Timeout cho requests
  timeout: '30s',
  
  // Thời gian nghỉ giữa các requests (giây)
  sleepMin: 1,
  sleepMax: 3,
  
  // Cấu hình logging
  logging: {
    detailedLogging: true,
    logLevel: 'info',
    logResponseBody: false,
  }
};

export default testConfig;