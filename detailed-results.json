{"root_group": {"name": "", "path": "", "id": "d41d8cd98f00b204e9800998ecf8427e", "groups": [], "checks": [{"passes": 102, "fails": 2, "name": "status is 200", "path": "::status is 200", "id": "6210a8cd14cd70477eba5c5e4cb3fb5f"}, {"name": "response time < 500ms", "path": "::response time < 500ms", "id": "3e02485a995423a591645f4eee6c60eb", "passes": 28, "fails": 76}, {"id": "5c0e70d8c221320a6668e681fb805817", "passes": 104, "fails": 0, "name": "response has body", "path": "::response has body"}, {"name": "content-type is JSON", "path": "::content-type is JSON", "id": "022bcf925e5f2a254931e1ad4ee313c5", "passes": 102, "fails": 2}, {"id": "284cbdc4479f7c4119a0c0ed4944a6e1", "passes": 52, "fails": 50, "name": "has status field", "path": "::has status field"}, {"name": "has data field", "path": "::has data field", "id": "d7964656e3d60131d1d643f3fd70f81d", "passes": 52, "fails": 50}, {"id": "25082ee8952e05b054833c54c74612fd", "passes": 102, "fails": 0, "name": "data is not empty", "path": "::data is not empty"}]}, "options": {"summaryTrendStats": ["avg", "min", "med", "max", "p(90)", "p(95)"], "summaryTimeUnit": "", "noColor": false}, "state": {"isStdOutTTY": true, "isStdErrTTY": true, "testRunDurationMs": 61270.093}, "metrics": {"http_req_connecting": {"type": "trend", "contains": "time", "values": {"p(90)": 0, "p(95)": 290.27375, "avg": 28.33088461538462, "min": 0, "med": 0, "max": 306.35}}, "http_req_receiving": {"type": "trend", "contains": "time", "values": {"max": 1.406, "p(90)": 0.19770000000000001, "p(95)": 0.26214999999999994, "avg": 0.12974038461538467, "min": 0.033, "med": 0.0975}}, "checks": {"type": "rate", "contains": "default", "values": {"rate": 0.7506925207756233, "passes": 542, "fails": 180}}, "http_req_blocked": {"contains": "time", "values": {"avg": 95.95983653846176, "min": 0, "med": 0.001, "max": 1008.467, "p(90)": 0.004700000000000003, "p(95)": 995.5615}, "type": "trend"}, "vus_max": {"type": "gauge", "contains": "default", "values": {"value": 10, "min": 10, "max": 10}}, "http_req_waiting": {"values": {"min": 281.961, "med": 1320.7835, "max": 6086.255, "p(90)": 3196.8468, "p(95)": 3624.5818999999997, "avg": 1507.8899615384605}, "type": "trend", "contains": "time"}, "vus": {"type": "gauge", "contains": "default", "values": {"value": 1, "min": 1, "max": 10}}, "http_reqs": {"type": "counter", "contains": "default", "values": {"count": 104, "rate": 1.697402352563754}}, "http_req_duration{expected_response:true}": {"type": "trend", "contains": "time", "values": {"p(90)": 3205.6148000000003, "p(95)": 3635.1868499999996, "avg": 1515.0221764705884, "min": 282.186, "med": 1320.949, "max": 6086.459}}, "iteration_duration": {"type": "trend", "contains": "time", "values": {"p(95)": 6236.24710805, "avg": 3617.8098766057683, "min": 1335.353541, "med": 3396.190291, "max": 7685.999584, "p(90)": 5635.5128875}}, "http_req_duration": {"type": "trend", "contains": "time", "values": {"p(90)": 3197.0884, "p(95)": 3625.6585499999997, "avg": 1508.2509807692309, "min": 282.186, "med": 1320.949, "max": 6086.459}, "thresholds": {"p(95)<5000": {"ok": true}}}, "http_req_failed": {"type": "rate", "contains": "default", "values": {"rate": 0.019230769230769232, "passes": 2, "fails": 102}, "thresholds": {"rate<0.5": {"ok": true}}}, "iterations": {"contains": "default", "values": {"count": 104, "rate": 1.697402352563754}, "type": "counter"}, "http_req_tls_handshaking": {"type": "trend", "contains": "time", "values": {"med": 0, "max": 659.471, "p(90)": 0, "p(95)": 654.2542, "avg": 62.81016346153846, "min": 0}}, "data_received": {"contains": "data", "values": {"count": 110692, "rate": 1806.6236654806448}, "type": "counter"}, "data_sent": {"type": "counter", "contains": "data", "values": {"count": 13107, "rate": 213.92165995243388}}, "http_req_sending": {"type": "trend", "contains": "time", "values": {"max": 2.005, "p(90)": 0.3675, "p(95)": 0.7366999999999998, "avg": 0.23127884615384617, "min": 0.044, "med": 0.143}}}}