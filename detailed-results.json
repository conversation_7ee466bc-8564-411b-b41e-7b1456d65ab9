{"options": {"summaryTrendStats": ["avg", "min", "med", "max", "p(90)", "p(95)"], "summaryTimeUnit": "", "noColor": false}, "state": {"testRunDurationMs": 66194.805, "isStdOutTTY": true, "isStdErrTTY": true}, "metrics": {"http_req_waiting": {"type": "trend", "contains": "time", "values": {"p(90)": 4062.2422, "p(95)": 5082.043199999998, "avg": 1707.5294897959186, "min": 276.989, "med": 1128.891, "max": 7806.258}}, "iterations": {"type": "counter", "contains": "default", "values": {"count": 49, "rate": 0.7402393586626624}}, "http_req_receiving": {"type": "trend", "contains": "time", "values": {"p(90)": 0.1806, "p(95)": 0.1956, "avg": 0.11181632653061228, "min": 0.018, "med": 0.105, "max": 0.28}}, "data_sent": {"values": {"count": 5234, "rate": 79.06964904572195}, "type": "counter", "contains": "data"}, "vus_max": {"type": "gauge", "contains": "default", "values": {"value": 3, "min": 3, "max": 3}}, "http_req_failed": {"type": "rate", "contains": "default", "values": {"rate": 0, "passes": 0, "fails": 49}, "thresholds": {"rate<0.01": {"ok": true}}}, "vus": {"contains": "default", "values": {"value": 1, "min": 1, "max": 3}, "type": "gauge"}, "http_req_sending": {"values": {"p(90)": 0.36640000000000006, "p(95)": 0.7143999999999993, "avg": 0.2614081632653061, "min": 0.049, "med": 0.162, "max": 2.421}, "type": "trend", "contains": "time"}, "http_req_blocked": {"type": "trend", "contains": "time", "values": {"min": 0, "med": 0.001, "max": 932.865, "p(90)": 0.004, "p(95)": 543.9867999999981, "avg": 56.52195918367352}}, "checks": {"type": "rate", "contains": "default", "values": {"passes": 250, "fails": 93, "rate": 0.7288629737609329}}, "data_received": {"values": {"count": 42748, "rate": 645.7908592675815}, "type": "counter", "contains": "data"}, "http_req_connecting": {"type": "trend", "contains": "time", "values": {"avg": 17.641000000000002, "min": 0, "med": 0, "max": 293.05, "p(90)": 0, "p(95)": 167.98079999999942}}, "http_req_duration": {"contains": "time", "values": {"avg": 1707.9027142857142, "min": 277.384, "med": 1129.344, "max": 7806.557, "p(90)": 4062.4148000000005, "p(95)": 5082.349399999998}, "thresholds": {"p(95)<1000": {"ok": false}}, "type": "trend"}, "http_req_tls_handshaking": {"type": "trend", "contains": "time", "values": {"med": 0, "max": 587.716, "p(90)": 0, "p(95)": 343.60799999999875, "avg": 35.64965306122449, "min": 0}}, "iteration_duration": {"type": "trend", "contains": "time", "values": {"p(90)": 5606.172249800001, "p(95)": 7177.186208799999, "avg": 3824.072238040816, "min": 1650.191709, "med": 3381.822, "max": 9929.723291}}, "http_req_duration{expected_response:true}": {"type": "trend", "contains": "time", "values": {"p(90)": 4062.4148000000005, "p(95)": 5082.349399999998, "avg": 1707.9027142857142, "min": 277.384, "med": 1129.344, "max": 7806.557}}, "http_reqs": {"type": "counter", "contains": "default", "values": {"count": 49, "rate": 0.7402393586626624}}}, "root_group": {"groups": [], "checks": [{"passes": 49, "fails": 0, "name": "status is 200", "path": "::status is 200", "id": "6210a8cd14cd70477eba5c5e4cb3fb5f"}, {"fails": 37, "name": "response time < 500ms", "path": "::response time < 500ms", "id": "3e02485a995423a591645f4eee6c60eb", "passes": 12}, {"id": "5c0e70d8c221320a6668e681fb805817", "passes": 49, "fails": 0, "name": "response has body", "path": "::response has body"}, {"name": "content-type is JSON", "path": "::content-type is JSON", "id": "022bcf925e5f2a254931e1ad4ee313c5", "passes": 49, "fails": 0}, {"name": "has status field", "path": "::has status field", "id": "284cbdc4479f7c4119a0c0ed4944a6e1", "passes": 21, "fails": 28}, {"name": "has data field", "path": "::has data field", "id": "d7964656e3d60131d1d643f3fd70f81d", "passes": 21, "fails": 28}, {"passes": 49, "fails": 0, "name": "data is not empty", "path": "::data is not empty", "id": "25082ee8952e05b054833c54c74612fd"}], "name": "", "path": "", "id": "d41d8cd98f00b204e9800998ecf8427e"}}