{"root_group": {"groups": [], "checks": [{"path": "::status is 200", "id": "6210a8cd14cd70477eba5c5e4cb3fb5f", "passes": 1334, "fails": 29, "name": "status is 200"}, {"fails": 1081, "name": "response time < 500ms", "path": "::response time < 500ms", "id": "3e02485a995423a591645f4eee6c60eb", "passes": 282}, {"passes": 1363, "fails": 0, "name": "response has body", "path": "::response has body", "id": "5c0e70d8c221320a6668e681fb805817"}, {"name": "content-type is JSON", "path": "::content-type is JSON", "id": "022bcf925e5f2a254931e1ad4ee313c5", "passes": 1334, "fails": 29}, {"path": "::has status field", "id": "284cbdc4479f7c4119a0c0ed4944a6e1", "passes": 652, "fails": 682, "name": "has status field"}, {"name": "has data field", "path": "::has data field", "id": "d7964656e3d60131d1d643f3fd70f81d", "passes": 652, "fails": 682}, {"name": "data is not empty", "path": "::data is not empty", "id": "25082ee8952e05b054833c54c74612fd", "passes": 1334, "fails": 0}], "name": "", "path": "", "id": "d41d8cd98f00b204e9800998ecf8427e"}, "options": {"summaryTrendStats": ["avg", "min", "med", "max", "p(90)", "p(95)"], "summaryTimeUnit": "", "noColor": false}, "state": {"isStdOutTTY": true, "isStdErrTTY": true, "testRunDurationMs": 543131.004}, "metrics": {"data_sent": {"type": "counter", "contains": "data", "values": {"count": 104821, "rate": 192.99395399641006}}, "vus_max": {"values": {"max": 10, "value": 10, "min": 10}, "type": "gauge", "contains": "default"}, "http_req_duration{expected_response:true}": {"contains": "time", "values": {"p(95)": 4044.92375, "avg": 1603.6801979010493, "min": 280.122, "med": 1331.433, "max": 11117.715, "p(90)": 3300.8569000000007}, "type": "trend"}, "checks": {"contains": "default", "values": {"rate": 0.7352443410196742, "passes": 6951, "fails": 2503}, "type": "rate"}, "iterations": {"type": "counter", "contains": "default", "values": {"count": 1363, "rate": 2.5095234666441546}}, "http_req_tls_handshaking": {"type": "trend", "contains": "time", "values": {"max": 624.29, "p(90)": 0, "p(95)": 0, "avg": 4.53171239911959, "min": 0, "med": 0}}, "data_received": {"type": "counter", "contains": "data", "values": {"rate": 1393.8257886673691, "count": 757030}}, "iteration_duration": {"type": "trend", "contains": "time", "values": {"min": 1317.209541, "med": 3335.898166, "max": 13483.86025, "p(90)": 5349.53925, "p(95)": 6168.233658, "avg": 3579.2315231944185}}, "http_req_blocked": {"type": "trend", "contains": "time", "values": {"p(95)": 0.004, "avg": 7.078769625825657, "min": 0, "med": 0.001, "max": 975.493, "p(90)": 0.004}}, "http_req_sending": {"values": {"med": 0.176, "max": 7.073, "p(90)": 0.6148, "p(95)": 0.9787999999999999, "avg": 0.31159134262655946, "min": 0.043}, "type": "trend", "contains": "time"}, "http_req_duration": {"values": {"avg": 1590.8316375641966, "min": 280.122, "med": 1307.077, "max": 11117.715, "p(90)": 3270.7526000000003, "p(95)": 4000.6949999999993}, "thresholds": {"p(95)<2000": {"ok": false}}, "type": "trend", "contains": "time"}, "vus": {"contains": "default", "values": {"value": 1, "min": 1, "max": 10}, "type": "gauge"}, "http_req_failed": {"type": "rate", "contains": "default", "values": {"rate": 0.02127659574468085, "passes": 29, "fails": 1334}, "thresholds": {"rate<0.2": {"ok": true}}}, "http_req_receiving": {"type": "trend", "contains": "time", "values": {"avg": 0.1518187820983126, "min": 0.017, "med": 0.107, "max": 3.107, "p(90)": 0.224, "p(95)": 0.29989999999999994}}, "http_req_waiting": {"type": "trend", "contains": "time", "values": {"avg": 1590.3682274394703, "min": 279.964, "med": 1306.77, "max": 11117.518, "p(90)": 3270.5338, "p(95)": 4000.3682999999996}}, "http_reqs": {"type": "counter", "contains": "default", "values": {"count": 1363, "rate": 2.5095234666441546}}, "http_req_connecting": {"contains": "time", "values": {"max": 304.976, "p(90)": 0, "p(95)": 0, "avg": 2.1681870873074103, "min": 0, "med": 0}, "type": "trend"}}}