import http from 'k6/http';
import { check, sleep } from 'k6';
import { SharedArray } from 'k6/data';
import { textSummary } from 'https://jslib.k6.io/k6-summary/0.0.1/index.js';

export const options = {
  "stages": [
    {
      "duration": "2m",
      "target": 10
    },
    {
      "duration": "5m",
      "target": 20
    },
    {
      "duration": "2m",
      "target": 30
    },
    {
      "duration": "5m",
      "target": 30
    },
    {
      "duration": "2m",
      "target": 0
    }
  ],
  "thresholds": {
    "http_req_duration": [
      "p(95)<3000"
    ],
    "http_req_failed": [
      "rate<0.3"
    ]
  }
};

const BASE_URL = __ENV.BASE_URL || 'https://httpbin.org';

const endpoints = new SharedArray('endpoints', function () {
  return [
    '/json',
    '/get',
    '/uuid',
    '/delay/1',
  ];
});

function generateRandomQuery() {
  const timestamp = Date.now();
  const random = Math.random().toString(36).substring(7);
  return `t=${timestamp}&r=${random}`;
}

function logRequestDetails(response, url, startTime) {
  const endTime = Date.now();
  const responseTime = endTime - startTime;
  
  const logEntry = {
    timestamp: new Date().toISOString(),
    url: url,
    method: 'GET',
    status_code: response.status,
    response_time_ms: responseTime,
    response_size_bytes: response.body ? response.body.length : 0,
    success: response.status >= 200 && response.status < 300,
    error: response.status >= 400 ? response.status_text : null,
    user_id: __VU,
    iteration: __ITER,
  };

  try {
    if (response.headers['Content-Type'] && 
        response.headers['Content-Type'].includes('application/json')) {
      const jsonData = JSON.parse(response.body);
      logEntry.response_data = {
        has_data: jsonData.hasOwnProperty('data'),
        has_status: jsonData.hasOwnProperty('status'),
        data_type: typeof jsonData.data,
        custom_fields: {
          user_id: jsonData.user_id || null,
          message: jsonData.message || null,
          result_count: Array.isArray(jsonData.data) ? jsonData.data.length : null,
        }
      };
    }
  } catch (e) {
    logEntry.json_parse_error = e.message;
  }

  console.log(JSON.stringify(logEntry));
  return logEntry;
}

export default function () {
  const startTime = Date.now();
  
  const endpoint = endpoints[Math.floor(Math.random() * endpoints.length)];
  const queryParams = generateRandomQuery();
  const url = `${BASE_URL}${endpoint}?${queryParams}`;
  
  const params = {
    headers: {
      'User-Agent': 'K6-LoadTest-NodeJS/1.0',
      'Accept': 'application/json',
      'Cache-Control': 'no-cache',
    },
    timeout: '30s',
  };

  const response = http.get(url, params);
  
  logRequestDetails(response, url, startTime);
  
  check(response, {
    'status is 200': (r) => r.status === 200,
    'response time < 500ms': (r) => r.timings.duration < 500,
    'response has body': (r) => r.body && r.body.length > 0,
    'content-type is JSON': (r) => 
      r.headers['Content-Type'] && 
      r.headers['Content-Type'].includes('application/json'),
  });

  if (response.status === 200 && response.body) {
    try {
      const jsonData = JSON.parse(response.body);
      
      check(jsonData, {
        'has status field': (data) => data.hasOwnProperty('status') || data.hasOwnProperty('url'),
        'has data field': (data) => data.hasOwnProperty('data') || data.hasOwnProperty('origin'),
        'data is not empty': (data) => {
          if (data.data) return data.data !== null && data.data !== '';
          return true;
        },
      });
    } catch (e) {
      console.log(`JSON parse error: ${e.message}`);
    }
  }

  sleep(Math.random() * 2 + 1);
}

export function handleSummary(data) {
  const summary = textSummary(data, { indent: ' ', enableColors: true });
  
  const detailedReport = {
    test_info: {
      start_time: new Date(data.state.testRunDurationMs).toISOString(),
      duration_ms: data.state.testRunDurationMs,
      vus_max: data.metrics.vus_max.values.max,
      iterations: data.metrics.iterations.values.count,
    },
    metrics: {
      http_req_duration: {
        avg: data.metrics.http_req_duration.values.avg,
        min: data.metrics.http_req_duration.values.min,
        max: data.metrics.http_req_duration.values.max,
        p90: data.metrics.http_req_duration.values['p(90)'],
        p95: data.metrics.http_req_duration.values['p(95)'],
        p99: data.metrics.http_req_duration.values['p(99)'],
      },
      http_req_failed: {
        rate: data.metrics.http_req_failed.values.rate,
        count: data.metrics.http_req_failed.values.count,
      },
      http_reqs: {
        count: data.metrics.http_reqs.values.count,
        rate: data.metrics.http_reqs.values.rate,
      },
      checks: {
        rate: data.metrics.checks.values.rate,
        passes: data.metrics.checks.values.passes,
        fails: data.metrics.checks.values.fails,
      }
    },
    thresholds: data.thresholds,
  };

  return {
    'stdout': summary,
    'summary.json': JSON.stringify(detailedReport, null, 2),
    'detailed-results.json': JSON.stringify(data, null, 2),
  };
}