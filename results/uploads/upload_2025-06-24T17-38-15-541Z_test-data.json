[{"timestamp": "2024-06-25T00:11:02.123Z", "url": "https://httpbin.org/json?t=1719273062123&r=abc123", "method": "GET", "status_code": 200, "response_time_ms": 245, "response_size_bytes": 429, "success": true, "user_id": 1, "iteration": 1, "response_data": {"has_data": true, "has_status": false, "data_type": "object", "custom_fields": {"user_id": null, "message": null, "result_count": null}}}, {"timestamp": "2024-06-25T00:11:04.456Z", "url": "https://httpbin.org/get?t=1719273064456&r=def456", "method": "GET", "status_code": 200, "response_time_ms": 189, "response_size_bytes": 312, "success": true, "user_id": 1, "iteration": 2, "response_data": {"has_data": false, "has_status": true, "data_type": "undefined", "custom_fields": {"user_id": null, "message": null, "result_count": null}}}, {"timestamp": "2024-06-25T00:11:06.789Z", "url": "https://httpbin.org/uuid?t=1719273066789&r=ghi789", "method": "GET", "status_code": 200, "response_time_ms": 156, "response_size_bytes": 53, "success": true, "user_id": 1, "iteration": 3, "response_data": {"has_data": false, "has_status": false, "data_type": "undefined", "custom_fields": {"user_id": null, "message": null, "result_count": null}}}, {"timestamp": "2024-06-25T00:11:09.012Z", "url": "https://httpbin.org/delay/1?t=1719273069012&r=jkl012", "method": "GET", "status_code": 200, "response_time_ms": 1234, "response_size_bytes": 298, "success": true, "user_id": 1, "iteration": 4, "response_data": {"has_data": false, "has_status": true, "data_type": "undefined", "custom_fields": {"user_id": null, "message": null, "result_count": null}}}, {"timestamp": "2024-06-25T00:11:11.345Z", "url": "https://httpbin.org/status/500?t=1719273071345&r=mno345", "method": "GET", "status_code": 500, "response_time_ms": 98, "response_size_bytes": 0, "success": false, "error": "Internal Server Error", "user_id": 1, "iteration": 5}]