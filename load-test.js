import http from 'k6/http';
import { check, sleep } from 'k6';
import { SharedArray } from 'k6/data';
import { textSummary } from 'https://jslib.k6.io/k6-summary/0.0.1/index.js';

// Cấu hình test
export const options = {
  stages: [
    { duration: '30s', target: 10 },  // Tăng dần lên 10 users trong 30s
    { duration: '1m', target: 20 },   // Tăng lên 20 users trong 1 phút
    { duration: '2m', target: 20 },   // Giữ 20 users trong 2 phút
    { duration: '30s', target: 0 },   // Giảm về 0 users trong 30s
  ],
  thresholds: {
    http_req_duration: ['p(95)<500'], // 95% requests phải dưới 500ms
    http_req_failed: ['rate<0.1'],    // Tỷ lệ lỗi dưới 10%
  },
};

// URL cần test - thay đổi theo API của bạn
const BASE_URL = __ENV.BASE_URL || 'https://httpbin.org/json';

// Mảng các endpoint để test (nếu có nhiều)
const endpoints = new SharedArray('endpoints', function () {
  return [
    '/json',
    '/get',
    '/uuid',
    // Thêm các endpoint khác nếu cần
  ];
});

// Hàm tạo query parameter ngẫu nhiên để tránh cache
function generateRandomQuery() {
  const timestamp = Date.now();
  const random = Math.random().toString(36).substring(7);
  return `t=${timestamp}&r=${random}`;
}

// Hàm ghi log chi tiết cho từng request
function logRequestDetails(response, url, startTime) {
  const endTime = Date.now();
  const responseTime = endTime - startTime;
  
  const logEntry = {
    timestamp: new Date().toISOString(),
    url: url,
    method: 'GET',
    status_code: response.status,
    response_time_ms: responseTime,
    response_size_bytes: response.body ? response.body.length : 0,
    success: response.status >= 200 && response.status < 300,
    error: response.status >= 400 ? response.status_text : null,
    user_id: __VU, // Virtual User ID
    iteration: __ITER, // Iteration number
  };

  // Thêm thông tin từ JSON response nếu có
  try {
    if (response.headers['Content-Type'] && 
        response.headers['Content-Type'].includes('application/json')) {
      const jsonData = JSON.parse(response.body);
      logEntry.response_data = {
        has_data: jsonData.hasOwnProperty('data'),
        has_status: jsonData.hasOwnProperty('status'),
        data_type: typeof jsonData.data,
        // Thêm các trường cụ thể bạn muốn kiểm tra
        custom_fields: {
          // Ví dụ: kiểm tra các trường quan trọng
          user_id: jsonData.user_id || null,
          message: jsonData.message || null,
          result_count: Array.isArray(jsonData.data) ? jsonData.data.length : null,
        }
      };
    }
  } catch (e) {
    logEntry.json_parse_error = e.message;
  }

  // In log ra console (sẽ được lưu vào file khi chạy với --console-output)
  console.log(JSON.stringify(logEntry));
  
  return logEntry;
}

// Hàm main test
export default function () {
  const startTime = Date.now();
  
  // Chọn endpoint ngẫu nhiên (nếu có nhiều)
  const endpoint = endpoints[Math.floor(Math.random() * endpoints.length)];
  const queryParams = generateRandomQuery();
  const url = `${BASE_URL}${endpoint}?${queryParams}`;
  
  // Cấu hình request headers
  const params = {
    headers: {
      'User-Agent': 'K6-LoadTest/1.0',
      'Accept': 'application/json',
      'Cache-Control': 'no-cache',
    },
    timeout: '30s',
  };

  // Gửi request
  const response = http.get(url, params);
  
  // Ghi log chi tiết
  const logEntry = logRequestDetails(response, url, startTime);
  
  // Kiểm tra response
  const checks = check(response, {
    'status is 200': (r) => r.status === 200,
    'response time < 500ms': (r) => r.timings.duration < 500,
    'response has body': (r) => r.body && r.body.length > 0,
    'content-type is JSON': (r) => 
      r.headers['Content-Type'] && 
      r.headers['Content-Type'].includes('application/json'),
  });

  // Kiểm tra JSON response nếu có
  if (response.status === 200 && response.body) {
    try {
      const jsonData = JSON.parse(response.body);
      
      // Kiểm tra các trường cụ thể trong JSON
      check(jsonData, {
        'has status field': (data) => data.hasOwnProperty('status') || data.hasOwnProperty('url'),
        'has data field': (data) => data.hasOwnProperty('data') || data.hasOwnProperty('origin'),
        // Thêm các kiểm tra khác tùy theo API của bạn
        'data is not empty': (data) => {
          if (data.data) return data.data !== null && data.data !== '';
          return true; // Skip nếu không có trường data
        },
      });
    } catch (e) {
      console.log(`JSON parse error: ${e.message}`);
    }
  }

  // Nghỉ ngẫu nhiên từ 1-3 giây giữa các request
  sleep(Math.random() * 2 + 1);
}

// Hàm tùy chỉnh summary output
export function handleSummary(data) {
  // Tạo summary text
  const summary = textSummary(data, { indent: ' ', enableColors: true });
  
  // Tạo detailed JSON report
  const detailedReport = {
    test_info: {
      start_time: new Date(data.state.testRunDurationMs).toISOString(),
      duration_ms: data.state.testRunDurationMs,
      vus_max: data.metrics.vus_max.values.max,
      iterations: data.metrics.iterations.values.count,
    },
    metrics: {
      http_req_duration: {
        avg: data.metrics.http_req_duration.values.avg,
        min: data.metrics.http_req_duration.values.min,
        max: data.metrics.http_req_duration.values.max,
        p90: data.metrics.http_req_duration.values['p(90)'],
        p95: data.metrics.http_req_duration.values['p(95)'],
        p99: data.metrics.http_req_duration.values['p(99)'],
      },
      http_req_failed: {
        rate: data.metrics.http_req_failed.values.rate,
        count: data.metrics.http_req_failed.values.count,
      },
      http_reqs: {
        count: data.metrics.http_reqs.values.count,
        rate: data.metrics.http_reqs.values.rate,
      },
      checks: {
        rate: data.metrics.checks.values.rate,
        passes: data.metrics.checks.values.passes,
        fails: data.metrics.checks.values.fails,
      }
    },
    thresholds: data.thresholds,
  };

  return {
    'stdout': summary,
    'summary.json': JSON.stringify(detailedReport, null, 2),
    'detailed-results.json': JSON.stringify(data, null, 2),
  };
}
