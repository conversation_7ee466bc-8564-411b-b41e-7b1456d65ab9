#!/bin/bash

# Script chạy K6 load test với logging chi tiết
# Sử dụng: ./run-test.sh [URL]

# Thiết lập màu sắc cho output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Hàm hiển thị help
show_help() {
    echo "Cách sử dụng: $0 [OPTIONS] [URL]"
    echo ""
    echo "OPTIONS:"
    echo "  -h, --help     Hiển thị help này"
    echo "  -u, --url      URL để test (mặc định: https://httpbin.org)"
    echo "  -d, --duration Thời gian test (mặc định: theo config trong script)"
    echo "  -v, --vus      Số virtual users (mặc định: theo config trong script)"
    echo ""
    echo "Ví dụ:"
    echo "  $0 https://api.example.com"
    echo "  $0 -u https://api.example.com"
    echo ""
}

# Xử lý tham số
URL="https://httpbin.org"
CUSTOM_OPTIONS=""

while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        -u|--url)
            URL="$2"
            shift 2
            ;;
        -d|--duration)
            CUSTOM_OPTIONS="$CUSTOM_OPTIONS --duration $2"
            shift 2
            ;;
        -v|--vus)
            CUSTOM_OPTIONS="$CUSTOM_OPTIONS --vus $2"
            shift 2
            ;;
        *)
            if [[ $1 =~ ^https?:// ]]; then
                URL="$1"
            else
                echo -e "${RED}Tham số không hợp lệ: $1${NC}"
                show_help
                exit 1
            fi
            shift
            ;;
    esac
done

# Kiểm tra K6 đã được cài đặt chưa
if ! command -v k6 &> /dev/null; then
    echo -e "${RED}K6 chưa được cài đặt!${NC}"
    echo "Cài đặt K6:"
    echo "  macOS: brew install k6"
    echo "  Ubuntu: sudo apt install k6"
    echo "  Windows: choco install k6"
    echo "  Hoặc tải từ: https://k6.io/docs/getting-started/installation/"
    exit 1
fi

# Tạo thư mục results nếu chưa có
mkdir -p results

# Tạo timestamp cho file kết quả
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
LOG_FILE="results/test_log_${TIMESTAMP}.json"
CONSOLE_FILE="results/console_output_${TIMESTAMP}.txt"

echo -e "${GREEN}=== K6 Load Test ===${NC}"
echo -e "URL: ${YELLOW}$URL${NC}"
echo -e "Log file: ${YELLOW}$LOG_FILE${NC}"
echo -e "Console output: ${YELLOW}$CONSOLE_FILE${NC}"
echo ""

# Chạy K6 test
echo -e "${GREEN}Bắt đầu load test...${NC}"
k6 run \
    --env BASE_URL="$URL" \
    --console-output="$CONSOLE_FILE" \
    --out json="$LOG_FILE" \
    $CUSTOM_OPTIONS \
    load-test.js

# Kiểm tra kết quả
if [ $? -eq 0 ]; then
    echo -e "${GREEN}✅ Test hoàn thành thành công!${NC}"
    echo ""
    echo -e "${GREEN}Kết quả được lưu tại:${NC}"
    echo -e "  📊 Summary: ${YELLOW}summary.json${NC}"
    echo -e "  📋 Detailed: ${YELLOW}detailed-results.json${NC}"
    echo -e "  📝 Raw logs: ${YELLOW}$LOG_FILE${NC}"
    echo -e "  💬 Console: ${YELLOW}$CONSOLE_FILE${NC}"
    echo ""
    echo -e "${GREEN}Để xem kết quả trong UI:${NC}"
    echo -e "  1. Mở file ${YELLOW}index.html${NC} trong trình duyệt"
    echo -e "  2. Kéo thả file ${YELLOW}$LOG_FILE${NC} hoặc ${YELLOW}summary.json${NC} vào trang"
    echo ""
else
    echo -e "${RED}❌ Test thất bại!${NC}"
    echo -e "Kiểm tra log tại: ${YELLOW}$CONSOLE_FILE${NC}"
    exit 1
fi

# Hiển thị quick stats nếu có jq
if command -v jq &> /dev/null && [ -f "summary.json" ]; then
    echo -e "${GREEN}=== Quick Stats ===${NC}"
    echo -n "Total requests: "
    jq -r '.metrics.http_reqs.count // "N/A"' summary.json
    echo -n "Average response time: "
    jq -r '(.metrics.http_req_duration.avg // 0) | (. * 1000 | floor) / 1000 | tostring + "ms"' summary.json
    echo -n "95th percentile: "
    jq -r '(.metrics.http_req_duration.p95 // 0) | (. * 1000 | floor) / 1000 | tostring + "ms"' summary.json
    echo -n "Error rate: "
    jq -r '(.metrics.http_req_failed.rate // 0) | (. * 100 | floor) / 100 | tostring + "%"' summary.json
fi
