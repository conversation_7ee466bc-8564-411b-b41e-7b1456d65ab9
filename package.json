{"name": "k6-load-test-analyzer", "version": "1.0.0", "description": "K6 Load Testing với UI phân tích kết quả trực quan - Node.js CLI Tool", "main": "src/index.js", "bin": {"k6-analyzer": "./bin/cli.js"}, "type": "module", "scripts": {"start": "node src/server.js", "dev": "nodemon src/server.js", "test": "node bin/cli.js test", "test:smoke": "node bin/cli.js test --type smoke", "test:load": "node bin/cli.js test --type load", "test:stress": "node bin/cli.js test --type stress", "test:spike": "node bin/cli.js test --type spike", "demo": "node bin/cli.js demo", "serve": "node src/server.js", "build": "npm run build:ui", "build:ui": "echo 'UI build completed'", "analyze": "node bin/cli.js analyze", "report": "node bin/cli.js report", "clean": "node bin/cli.js clean", "install:k6": "node scripts/install-k6.js", "postinstall": "node scripts/check-dependencies.js"}, "keywords": ["k6", "load-testing", "performance", "testing", "charts", "analytics", "nodejs", "cli", "express"], "author": "Your Name", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/yourusername/k6-load-test-analyzer.git"}, "bugs": {"url": "https://github.com/yourusername/k6-load-test-analyzer/issues"}, "homepage": "https://github.com/yourusername/k6-load-test-analyzer#readme", "dependencies": {"commander": "^11.1.0", "express": "^4.18.2", "cors": "^2.8.5", "multer": "^1.4.5-lts.1", "chalk": "^5.3.0", "ora": "^7.0.1", "inquirer": "^9.2.12", "fs-extra": "^11.1.1", "moment": "^2.29.4", "open": "^9.1.0", "node-cron": "^3.0.3", "compression": "^1.7.4", "helmet": "^7.1.0"}, "devDependencies": {"nodemon": "^3.0.2", "eslint": "^8.55.0", "prettier": "^3.1.1"}, "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}, "files": ["bin/", "src/", "public/", "k6/", "scripts/", "README.md"], "packageManager": "yarn@1.22.19+sha1.4ba7fc5c6e704fce2066ecbfb0b0d8976fe62447"}