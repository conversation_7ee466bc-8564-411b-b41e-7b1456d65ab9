// Cấ<PERSON> hình mẫu cho K6 Load Test
// Copy file này thành config.js và tùy chỉnh theo nhu cầu

export const testConfig = {
  // URL cơ bản để test
  baseUrl: 'https://httpbin.org',
  
  // Danh sách endpoints để test
  endpoints: [
    '/json',
    '/get',
    '/uuid',
    '/delay/1',
  ],
  
  // Cấu hình stages (load pattern)
  stages: [
    { duration: '30s', target: 5 },   // Warm up: 5 users trong 30s
    { duration: '1m', target: 10 },   // Ramp up: tăng lên 10 users
    { duration: '3m', target: 10 },   // Steady: giữ 10 users trong 3 phút
    { duration: '30s', target: 20 },  // Peak: tăng lên 20 users
    { duration: '1m', target: 20 },   // Peak load: giữ 20 users
    { duration: '30s', target: 0 },   // Ramp down: giảm về 0
  ],
  
  // Ngưỡng chấp nhận (thresholds)
  thresholds: {
    // 95% requests phải hoàn thành dưới 500ms
    http_req_duration: ['p(95)<500'],
    
    // 99% requests phải hoàn thành dưới 1000ms
    'http_req_duration{expected_response:true}': ['p(99)<1000'],
    
    // Tỷ lệ lỗi phải dưới 5%
    http_req_failed: ['rate<0.05'],
    
    // Tỷ lệ check thành công phải trên 95%
    checks: ['rate>0.95'],
  },
  
  // Headers mặc định
  defaultHeaders: {
    'User-Agent': 'K6-LoadTest/1.0',
    'Accept': 'application/json',
    'Cache-Control': 'no-cache',
  },
  
  // Timeout cho requests
  timeout: '30s',
  
  // Thời gian nghỉ giữa các requests (giây)
  sleepMin: 1,
  sleepMax: 3,
  
  // Cấu hình cho từng loại test
  testTypes: {
    // Smoke test - test cơ bản
    smoke: {
      stages: [
        { duration: '1m', target: 1 },
      ],
      thresholds: {
        http_req_duration: ['p(95)<1000'],
        http_req_failed: ['rate<0.01'],
      }
    },
    
    // Load test - test tải bình thường
    load: {
      stages: [
        { duration: '2m', target: 10 },
        { duration: '5m', target: 10 },
        { duration: '2m', target: 0 },
      ],
      thresholds: {
        http_req_duration: ['p(95)<500'],
        http_req_failed: ['rate<0.05'],
      }
    },
    
    // Stress test - test tải cao
    stress: {
      stages: [
        { duration: '2m', target: 10 },
        { duration: '5m', target: 20 },
        { duration: '2m', target: 30 },
        { duration: '5m', target: 30 },
        { duration: '2m', target: 0 },
      ],
      thresholds: {
        http_req_duration: ['p(95)<1000'],
        http_req_failed: ['rate<0.1'],
      }
    },
    
    // Spike test - test đột biến
    spike: {
      stages: [
        { duration: '1m', target: 10 },
        { duration: '30s', target: 50 },
        { duration: '1m', target: 10 },
        { duration: '30s', target: 0 },
      ],
      thresholds: {
        http_req_duration: ['p(95)<2000'],
        http_req_failed: ['rate<0.15'],
      }
    }
  },
  
  // Cấu hình cho các môi trường
  environments: {
    dev: {
      baseUrl: 'https://dev-api.example.com',
      thresholds: {
        http_req_duration: ['p(95)<1000'],
        http_req_failed: ['rate<0.1'],
      }
    },
    
    staging: {
      baseUrl: 'https://staging-api.example.com',
      thresholds: {
        http_req_duration: ['p(95)<800'],
        http_req_failed: ['rate<0.05'],
      }
    },
    
    production: {
      baseUrl: 'https://api.example.com',
      thresholds: {
        http_req_duration: ['p(95)<500'],
        http_req_failed: ['rate<0.01'],
      }
    }
  },
  
  // Cấu hình authentication
  auth: {
    // API Key
    apiKey: {
      header: 'X-API-Key',
      value: 'your-api-key-here'
    },
    
    // Bearer Token
    bearerToken: {
      header: 'Authorization',
      value: 'Bearer your-token-here'
    },
    
    // Basic Auth
    basicAuth: {
      username: 'your-username',
      password: 'your-password'
    }
  },
  
  // Dữ liệu test
  testData: {
    // Dữ liệu cho POST requests
    samplePayloads: [
      {
        name: 'John Doe',
        email: '<EMAIL>',
        age: 30
      },
      {
        name: 'Jane Smith',
        email: '<EMAIL>',
        age: 25
      }
    ],
    
    // Query parameters mẫu
    sampleQueries: [
      { page: 1, limit: 10 },
      { page: 2, limit: 20 },
      { sort: 'name', order: 'asc' }
    ]
  },
  
  // Cấu hình logging
  logging: {
    // Có log chi tiết từng request không
    detailedLogging: true,
    
    // Các trường cần log
    logFields: [
      'timestamp',
      'url',
      'method',
      'status_code',
      'response_time_ms',
      'success',
      'user_id',
      'iteration'
    ],
    
    // Có log response body không (chỉ nên bật khi debug)
    logResponseBody: false,
    
    // Log level: 'error', 'warn', 'info', 'debug'
    logLevel: 'info'
  }
};

// Export default config
export default testConfig;
