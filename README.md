# 🚀 K6 Load Testing & Results Analyzer

Hệ thống load testing hoàn chỉnh với K6 và UI phân tích kết quả trực quan.

## 📋 Tổng quan

Dự án này bao gồm:
- **K6 Script**: Script load testing với logging chi tiết
- **Web UI**: Gia<PERSON> di<PERSON>n phân tích kết quả với biểu đồ tương tác
- **Automation**: Script tự động chạy test và lưu kết quả

## 🛠️ Cài đặt

### 1. Cài đặt K6

**macOS:**
```bash
brew install k6
```

**Ubuntu/Debian:**
```bash
sudo gpg -k
sudo gpg --no-default-keyring --keyring /usr/share/keyrings/k6-archive-keyring.gpg --keyserver hkp://keyserver.ubuntu.com:80 --recv-keys C5AD17C747E3415A3642D57D77C6C491D6AC1D69
echo "deb [signed-by=/usr/share/keyrings/k6-archive-keyring.gpg] https://dl.k6.io/deb stable main" | sudo tee /etc/apt/sources.list.d/k6.list
sudo apt-get update
sudo apt-get install k6
```

**Windows:**
```bash
choco install k6
```

### 2. Clone hoặc tải project
```bash
git clone <repository-url>
cd loadtest
```

## 🚀 Cách sử dụng

### 1. Chạy Load Test

**Cách đơn giản:**
```bash
./run-test.sh https://api.example.com
```

**Với tùy chọn:**
```bash
./run-test.sh -u https://api.example.com -v 50 -d 5m
```

**Chạy trực tiếp với K6:**
```bash
k6 run --env BASE_URL="https://api.example.com" load-test.js
```

### 2. Xem kết quả trong UI

1. Mở file `index.html` trong trình duyệt
2. Kéo thả file JSON kết quả vào trang
3. Sử dụng bộ lọc để phân tích chi tiết

## 📊 Các file kết quả

Sau khi chạy test, bạn sẽ có các file:

- `results/test_log_YYYYMMDD_HHMMSS.json` - Raw data từ K6
- `summary.json` - Tóm tắt metrics
- `detailed-results.json` - Kết quả chi tiết
- `results/console_output_YYYYMMDD_HHMMSS.txt` - Console output

## 🎯 Tính năng K6 Script

### Cấu hình Test
```javascript
export const options = {
  stages: [
    { duration: '30s', target: 10 },  // Ramp up
    { duration: '1m', target: 20 },   // Stay at 20 users
    { duration: '2m', target: 20 },   // Load test
    { duration: '30s', target: 0 },   // Ramp down
  ],
  thresholds: {
    http_req_duration: ['p(95)<500'], // 95% < 500ms
    http_req_failed: ['rate<0.1'],    // Error rate < 10%
  },
};
```

### Tính năng chính:
- ✅ Query parameters động để tránh cache
- ✅ Kiểm tra JSON response
- ✅ Logging chi tiết từng request
- ✅ Thresholds tự động
- ✅ Multiple endpoints support
- ✅ Custom headers và timeout

### Ví dụ log output:
```json
{
  "timestamp": "2024-01-15T10:30:45.123Z",
  "url": "https://api.example.com/users?t=1705312245123&r=abc123",
  "method": "GET",
  "status_code": 200,
  "response_time_ms": 245,
  "success": true,
  "user_id": 1,
  "iteration": 5,
  "response_data": {
    "has_data": true,
    "has_status": true,
    "custom_fields": {
      "user_id": "12345",
      "result_count": 10
    }
  }
}
```

## 📈 Tính năng UI Analyzer

### Biểu đồ có sẵn:
1. **Response Time Timeline** - Response time theo thời gian
2. **Success/Failure Rate** - Tỷ lệ thành công/thất bại
3. **Status Code Distribution** - Phân phối mã trạng thái
4. **Requests per Second** - Số request mỗi giây

### Bộ lọc:
- 🔍 Lọc theo status (success/error)
- ⏰ Lọc theo khoảng thời gian
- 🚀 Lọc theo response time tối đa
- 🔄 Reset filters

### Stats Overview:
- Total Requests
- Average Response Time
- Success Rate (%)
- P95 Response Time
- Error Rate (%)
- Requests/Second

## ⚙️ Tùy chỉnh

### Thay đổi cấu hình test:
Chỉnh sửa file `load-test.js`:
```javascript
// Thay đổi số lượng users và thời gian
export const options = {
  stages: [
    { duration: '1m', target: 50 },   // 50 users
    { duration: '5m', target: 50 },   // 5 phút
  ],
};

// Thay đổi thresholds
thresholds: {
  http_req_duration: ['p(95)<1000'], // 1 giây
  http_req_failed: ['rate<0.05'],    // 5% error rate
},
```

### Thêm endpoints:
```javascript
const endpoints = new SharedArray('endpoints', function () {
  return [
    '/api/users',
    '/api/products',
    '/api/orders',
  ];
});
```

### Custom checks:
```javascript
check(jsonData, {
  'has user_id': (data) => data.user_id !== undefined,
  'data array not empty': (data) => Array.isArray(data.items) && data.items.length > 0,
});
```

## 🐛 Troubleshooting

### K6 không chạy được:
```bash
# Kiểm tra version
k6 version

# Kiểm tra script syntax
k6 run --no-usage-report load-test.js
```

### UI không hiển thị dữ liệu:
1. Kiểm tra file JSON có đúng format không
2. Mở Developer Tools (F12) để xem lỗi
3. Thử với file `summary.json` thay vì raw log

### Performance issues:
- Giảm số lượng VUs nếu máy yếu
- Tăng `sleep()` time giữa requests
- Sử dụng `--quiet` flag để giảm output

## 📝 Ví dụ sử dụng

### Test API đơn giản:
```bash
./run-test.sh https://jsonplaceholder.typicode.com/posts
```

### Test với authentication:
Chỉnh sửa `load-test.js`:
```javascript
const params = {
  headers: {
    'Authorization': 'Bearer your-token-here',
    'Content-Type': 'application/json',
  },
};
```

### Test POST request:
```javascript
const payload = JSON.stringify({
  title: 'Test Post',
  body: 'Test content',
});

const response = http.post(url, payload, params);
```

## 🤝 Đóng góp

1. Fork repository
2. Tạo feature branch
3. Commit changes
4. Push và tạo Pull Request

## 📄 License

MIT License - xem file LICENSE để biết thêm chi tiết.
