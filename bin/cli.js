#!/usr/bin/env node

import { Command } from 'commander';
import chalk from 'chalk';
import ora from 'ora';
import inquirer from 'inquirer';
import fs from 'fs-extra';
import path from 'path';
import { fileURLToPath } from 'url';
import { spawn } from 'child_process';
import open from 'open';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const rootDir = path.join(__dirname, '..');

// Import modules
import { TestRunner } from '../src/test-runner.js';
import { ResultAnalyzer } from '../src/result-analyzer.js';
import { ConfigManager } from '../src/config-manager.js';
import { ServerManager } from '../src/server-manager.js';

const program = new Command();

program
  .name('k6-analyzer')
  .description('K6 Load Testing với UI phân tích kết quả trực quan')
  .version('1.0.0');

// Test command
program
  .command('test')
  .description('Chạy load test với K6')
  .option('-u, --url <url>', 'URL để test')
  .option('-t, --type <type>', 'Loại test (smoke|load|stress|spike)', 'load')
  .option('-d, --duration <duration>', 'Thời gian test (ví dụ: 5m, 30s)')
  .option('-v, --vus <number>', 'Số virtual users', '10')
  .option('-c, --config <file>', 'File cấu hình')
  .option('--no-ui', 'Không mở UI sau khi test')
  .action(async (options) => {
    const spinner = ora('Đang chuẩn bị load test...').start();
    
    try {
      const testRunner = new TestRunner();
      
      // Kiểm tra K6
      if (!await testRunner.checkK6()) {
        spinner.fail('K6 chưa được cài đặt!');
        console.log(chalk.yellow('\nCài đặt K6:'));
        console.log('  macOS: brew install k6');
        console.log('  Ubuntu: sudo apt install k6');
        console.log('  Windows: choco install k6');
        process.exit(1);
      }
      
      spinner.text = 'Đang chạy load test...';
      
      const result = await testRunner.runTest(options);
      
      spinner.succeed('Load test hoàn thành!');
      
      console.log(chalk.green('\n📊 Kết quả:'));
      console.log(`  • File log: ${chalk.yellow(result.logFile)}`);
      console.log(`  • Summary: ${chalk.yellow(result.summaryFile)}`);
      
      if (options.ui !== false) {
        console.log(chalk.blue('\n🌐 Đang mở UI...'));
        await ServerManager.startServer();
        await open('http://localhost:3000');
      }
      
    } catch (error) {
      spinner.fail('Lỗi khi chạy test: ' + error.message);
      process.exit(1);
    }
  });

// Demo command
program
  .command('demo')
  .description('Chạy demo test nhanh')
  .action(async () => {
    const spinner = ora('Đang chạy demo...').start();
    
    try {
      const testRunner = new TestRunner();
      
      const demoOptions = {
        url: 'https://httpbin.org',
        type: 'smoke',
        duration: '1m',
        vus: '3',
        noThresholds: true  // Bỏ qua thresholds cho demo
      };
      
      const result = await testRunner.runTest(demoOptions);
      
      spinner.succeed('Demo hoàn thành!');
      
      console.log(chalk.green('\n🎉 Demo test thành công!'));
      console.log(chalk.blue('🌐 Đang mở UI để xem kết quả...'));
      
      await ServerManager.startServer();
      await open('http://localhost:3000');
      
    } catch (error) {
      spinner.fail('Lỗi demo: ' + error.message);
      process.exit(1);
    }
  });

// Analyze command
program
  .command('analyze')
  .description('Phân tích file kết quả có sẵn')
  .option('-f, --file <file>', 'File JSON kết quả')
  .option('-d, --dir <directory>', 'Thư mục chứa kết quả', './results')
  .action(async (options) => {
    try {
      const analyzer = new ResultAnalyzer();
      
      if (options.file) {
        await analyzer.analyzeFile(options.file);
      } else {
        await analyzer.analyzeDirectory(options.dir);
      }
      
      console.log(chalk.blue('\n🌐 Đang mở UI để xem phân tích...'));
      await ServerManager.startServer();
      await open('http://localhost:3000');
      
    } catch (error) {
      console.error(chalk.red('Lỗi phân tích: ' + error.message));
      process.exit(1);
    }
  });

// Server command
program
  .command('serve')
  .description('Khởi động web server')
  .option('-p, --port <port>', 'Port để chạy server', '3000')
  .action(async (options) => {
    try {
      console.log(chalk.blue('🚀 Đang khởi động server...'));
      await ServerManager.startServer(options.port);
      console.log(chalk.green(`✅ Server đang chạy tại: http://localhost:${options.port}`));
      
      await open(`http://localhost:${options.port}`);
      
    } catch (error) {
      console.error(chalk.red('Lỗi khởi động server: ' + error.message));
      process.exit(1);
    }
  });

// Config command
program
  .command('config')
  .description('Quản lý cấu hình')
  .option('--init', 'Tạo file cấu hình mới')
  .option('--edit', 'Chỉnh sửa cấu hình')
  .option('--show', 'Hiển thị cấu hình hiện tại')
  .action(async (options) => {
    try {
      const configManager = new ConfigManager();
      
      if (options.init) {
        await configManager.initConfig();
        console.log(chalk.green('✅ Đã tạo file cấu hình mới: config.js'));
      } else if (options.edit) {
        await configManager.editConfig();
      } else if (options.show) {
        await configManager.showConfig();
      } else {
        console.log(chalk.yellow('Sử dụng --init, --edit, hoặc --show'));
      }
      
    } catch (error) {
      console.error(chalk.red('Lỗi cấu hình: ' + error.message));
      process.exit(1);
    }
  });

// Report command
program
  .command('report')
  .description('Tạo báo cáo từ kết quả test')
  .option('-f, --file <file>', 'File JSON kết quả')
  .option('-o, --output <file>', 'File output (HTML/PDF)')
  .option('--format <format>', 'Định dạng báo cáo (html|pdf|json)', 'html')
  .action(async (options) => {
    const spinner = ora('Đang tạo báo cáo...').start();
    
    try {
      const analyzer = new ResultAnalyzer();
      const reportFile = await analyzer.generateReport(options);
      
      spinner.succeed(`Báo cáo đã được tạo: ${reportFile}`);
      
      if (options.format === 'html') {
        await open(reportFile);
      }
      
    } catch (error) {
      spinner.fail('Lỗi tạo báo cáo: ' + error.message);
      process.exit(1);
    }
  });

// Clean command
program
  .command('clean')
  .description('Dọn dẹp file kết quả cũ')
  .option('--days <days>', 'Xóa file cũ hơn X ngày', '7')
  .option('--force', 'Xóa tất cả không hỏi')
  .action(async (options) => {
    try {
      const resultsDir = path.join(rootDir, 'results');
      
      if (!options.force) {
        const { confirm } = await inquirer.prompt([
          {
            type: 'confirm',
            name: 'confirm',
            message: `Xóa file kết quả cũ hơn ${options.days} ngày?`,
            default: false
          }
        ]);
        
        if (!confirm) {
          console.log(chalk.yellow('Đã hủy.'));
          return;
        }
      }
      
      const files = await fs.readdir(resultsDir);
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - parseInt(options.days));
      
      let deletedCount = 0;
      
      for (const file of files) {
        const filePath = path.join(resultsDir, file);
        const stats = await fs.stat(filePath);
        
        if (stats.mtime < cutoffDate) {
          await fs.remove(filePath);
          deletedCount++;
        }
      }
      
      console.log(chalk.green(`✅ Đã xóa ${deletedCount} file cũ.`));
      
    } catch (error) {
      console.error(chalk.red('Lỗi dọn dẹp: ' + error.message));
      process.exit(1);
    }
  });

// Install command
program
  .command('install')
  .description('Cài đặt dependencies')
  .option('--k6', 'Cài đặt K6')
  .action(async (options) => {
    if (options.k6) {
      const spinner = ora('Đang cài đặt K6...').start();
      
      try {
        // Logic cài đặt K6 tùy theo OS
        const platform = process.platform;
        
        if (platform === 'darwin') {
          // macOS
          await execCommand('brew install k6');
        } else if (platform === 'linux') {
          // Linux
          console.log(chalk.yellow('Vui lòng cài đặt K6 thủ công:'));
          console.log('sudo apt install k6');
        } else {
          // Windows
          console.log(chalk.yellow('Vui lòng cài đặt K6 thủ công:'));
          console.log('choco install k6');
        }
        
        spinner.succeed('K6 đã được cài đặt!');
        
      } catch (error) {
        spinner.fail('Lỗi cài đặt K6: ' + error.message);
      }
    }
  });

// Helper function
function execCommand(command) {
  return new Promise((resolve, reject) => {
    const child = spawn(command, { shell: true, stdio: 'inherit' });
    child.on('close', (code) => {
      if (code === 0) {
        resolve();
      } else {
        reject(new Error(`Command failed with code ${code}`));
      }
    });
  });
}

program.parse();
