<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>K6 Load Test Results Analyzer</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/date-fns@2.29.3/index.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .header p {
            font-size: 1.1em;
            opacity: 0.9;
        }
        
        .upload-section {
            padding: 30px;
            text-align: center;
            border-bottom: 1px solid #eee;
        }
        
        .upload-area {
            border: 3px dashed #ddd;
            border-radius: 10px;
            padding: 40px;
            margin: 20px 0;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        .upload-area:hover, .upload-area.dragover {
            border-color: #4facfe;
            background: #f8f9ff;
        }
        
        .upload-icon {
            font-size: 3em;
            color: #ddd;
            margin-bottom: 15px;
        }
        
        .upload-area.dragover .upload-icon {
            color: #4facfe;
        }
        
        .file-input {
            display: none;
        }
        
        .btn {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 1em;
            transition: transform 0.2s ease;
        }
        
        .btn:hover {
            transform: translateY(-2px);
        }
        
        .filters {
            padding: 20px 30px;
            background: #f8f9fa;
            border-bottom: 1px solid #eee;
            display: none;
        }
        
        .filter-group {
            display: inline-block;
            margin-right: 20px;
            margin-bottom: 10px;
        }
        
        .filter-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 600;
            color: #333;
        }
        
        .filter-group select, .filter-group input {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
        }
        
        .stats-overview {
            padding: 30px;
            display: none;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
        }
        
        .stat-value {
            font-size: 2em;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .stat-label {
            font-size: 0.9em;
            opacity: 0.9;
        }
        
        .charts-section {
            padding: 30px;
            display: none;
        }
        
        .chart-container {
            margin-bottom: 40px;
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .chart-title {
            font-size: 1.3em;
            font-weight: 600;
            margin-bottom: 20px;
            color: #333;
            text-align: center;
        }
        
        .chart-wrapper {
            position: relative;
            height: 400px;
        }
        
        .error-message {
            background: #ff6b6b;
            color: white;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
            display: none;
        }
        
        .success-message {
            background: #51cf66;
            color: white;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
            display: none;
        }
        
        @media (max-width: 768px) {
            .container {
                margin: 10px;
                border-radius: 10px;
            }
            
            .header {
                padding: 20px;
            }
            
            .header h1 {
                font-size: 2em;
            }
            
            .stats-grid {
                grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
                gap: 15px;
            }
            
            .chart-wrapper {
                height: 300px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📊 K6 Load Test Analyzer</h1>
            <p>Phân tích kết quả load testing một cách trực quan và chi tiết</p>
        </div>
        
        <div class="upload-section">
            <h2>📁 Tải file kết quả</h2>
            <p>Kéo thả file JSON từ K6 hoặc click để chọn file</p>
            
            <div class="upload-area" id="uploadArea">
                <div class="upload-icon">📄</div>
                <p><strong>Kéo thả file JSON vào đây</strong></p>
                <p>hoặc</p>
                <button class="btn" onclick="document.getElementById('fileInput').click()">
                    Chọn file
                </button>
            </div>
            
            <input type="file" id="fileInput" class="file-input" accept=".json" multiple>
            
            <div class="error-message" id="errorMessage"></div>
            <div class="success-message" id="successMessage"></div>
        </div>
        
        <div class="filters" id="filtersSection">
            <h3>🔍 Bộ lọc</h3>
            <div class="filter-group">
                <label>Trạng thái:</label>
                <select id="statusFilter">
                    <option value="all">Tất cả</option>
                    <option value="success">Thành công (2xx)</option>
                    <option value="error">Lỗi (4xx, 5xx)</option>
                </select>
            </div>
            
            <div class="filter-group">
                <label>Thời gian từ:</label>
                <input type="datetime-local" id="timeFromFilter">
            </div>
            
            <div class="filter-group">
                <label>Thời gian đến:</label>
                <input type="datetime-local" id="timeToFilter">
            </div>
            
            <div class="filter-group">
                <label>Response time tối đa (ms):</label>
                <input type="number" id="responseTimeFilter" placeholder="1000">
            </div>
            
            <button class="btn" onclick="applyFilters()">Áp dụng bộ lọc</button>
            <button class="btn" onclick="resetFilters()">Reset</button>
        </div>
        
        <div class="stats-overview" id="statsSection">
            <h2>📈 Tổng quan</h2>
            <div class="stats-grid" id="statsGrid">
                <!-- Stats sẽ được tạo động bằng JavaScript -->
            </div>
        </div>
        
        <div class="charts-section" id="chartsSection">
            <div class="chart-container">
                <div class="chart-title">⏱️ Response Time theo thời gian</div>
                <div class="chart-wrapper">
                    <canvas id="responseTimeChart"></canvas>
                </div>
            </div>
            
            <div class="chart-container">
                <div class="chart-title">✅ Tỷ lệ thành công/thất bại</div>
                <div class="chart-wrapper">
                    <canvas id="successRateChart"></canvas>
                </div>
            </div>
            
            <div class="chart-container">
                <div class="chart-title">📊 Phân phối Status Code</div>
                <div class="chart-wrapper">
                    <canvas id="statusCodeChart"></canvas>
                </div>
            </div>
            
            <div class="chart-container">
                <div class="chart-title">🚀 Requests per Second</div>
                <div class="chart-wrapper">
                    <canvas id="rpsChart"></canvas>
                </div>
            </div>
        </div>
    </div>

    <script src="analyzer.js"></script>
</body>
</html>
