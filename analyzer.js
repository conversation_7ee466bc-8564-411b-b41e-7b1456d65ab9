// Global variables
let rawData = [];
let filteredData = [];
let charts = {};

// DOM elements
const uploadArea = document.getElementById('uploadArea');
const fileInput = document.getElementById('fileInput');
const errorMessage = document.getElementById('errorMessage');
const successMessage = document.getElementById('successMessage');
const filtersSection = document.getElementById('filtersSection');
const statsSection = document.getElementById('statsSection');
const chartsSection = document.getElementById('chartsSection');

// Event listeners
uploadArea.addEventListener('click', () => fileInput.click());
uploadArea.addEventListener('dragover', handleDragOver);
uploadArea.addEventListener('drop', handleDrop);
uploadArea.addEventListener('dragleave', handleDragLeave);
fileInput.addEventListener('change', handleFileSelect);

// Drag and drop handlers
function handleDragOver(e) {
    e.preventDefault();
    uploadArea.classList.add('dragover');
}

function handleDragLeave(e) {
    e.preventDefault();
    uploadArea.classList.remove('dragover');
}

function handleDrop(e) {
    e.preventDefault();
    uploadArea.classList.remove('dragover');
    const files = e.dataTransfer.files;
    processFiles(files);
}

function handleFileSelect(e) {
    const files = e.target.files;
    processFiles(files);
}

// File processing
function processFiles(files) {
    if (files.length === 0) return;
    
    const file = files[0];
    if (!file.name.endsWith('.json')) {
        showError('Vui lòng chọn file JSON!');
        return;
    }
    
    const reader = new FileReader();
    reader.onload = function(e) {
        try {
            const data = JSON.parse(e.target.result);
            processData(data);
            showSuccess(`Đã tải thành công file: ${file.name}`);
        } catch (error) {
            showError('Lỗi đọc file JSON: ' + error.message);
        }
    };
    reader.readAsText(file);
}

// Data processing
function processData(data) {
    rawData = [];
    
    // Xử lý dữ liệu từ K6 JSON output
    if (data.type === 'Point' && data.data) {
        // Raw K6 output format
        rawData.push(data.data);
    } else if (Array.isArray(data)) {
        // Array of data points
        rawData = data;
    } else if (data.metrics) {
        // Summary format
        processSummaryData(data);
        return;
    } else {
        // Try to parse console output format (JSON lines)
        const lines = data.split('\n').filter(line => line.trim());
        lines.forEach(line => {
            try {
                const parsed = JSON.parse(line);
                if (parsed.timestamp && parsed.response_time_ms !== undefined) {
                    rawData.push(parsed);
                }
            } catch (e) {
                // Skip invalid lines
            }
        });
    }
    
    if (rawData.length === 0) {
        showError('Không tìm thấy dữ liệu hợp lệ trong file!');
        return;
    }
    
    filteredData = [...rawData];
    updateUI();
}

function processSummaryData(data) {
    // Tạo dữ liệu giả từ summary để hiển thị
    const metrics = data.metrics;
    const duration = data.test_info?.duration_ms || 60000;
    const requests = metrics.http_reqs?.count || 100;
    
    rawData = [];
    const startTime = new Date().getTime() - duration;
    
    for (let i = 0; i < requests; i++) {
        const timestamp = new Date(startTime + (i * duration / requests));
        const responseTime = Math.random() * metrics.http_req_duration?.max || 500;
        const success = Math.random() > (metrics.http_req_failed?.rate || 0);
        
        rawData.push({
            timestamp: timestamp.toISOString(),
            response_time_ms: responseTime,
            status_code: success ? 200 : (Math.random() > 0.5 ? 404 : 500),
            success: success,
            url: 'https://example.com/api',
            method: 'GET'
        });
    }
    
    filteredData = [...rawData];
    updateUI();
}

// UI Updates
function updateUI() {
    showFilters();
    updateStats();
    updateCharts();
}

function showFilters() {
    filtersSection.style.display = 'block';
    statsSection.style.display = 'block';
    chartsSection.style.display = 'block';
}

function updateStats() {
    const stats = calculateStats(filteredData);
    const statsGrid = document.getElementById('statsGrid');
    
    statsGrid.innerHTML = `
        <div class="stat-card">
            <div class="stat-value">${stats.totalRequests}</div>
            <div class="stat-label">Tổng requests</div>
        </div>
        <div class="stat-card">
            <div class="stat-value">${stats.avgResponseTime}ms</div>
            <div class="stat-label">Response time TB</div>
        </div>
        <div class="stat-card">
            <div class="stat-value">${stats.successRate}%</div>
            <div class="stat-label">Tỷ lệ thành công</div>
        </div>
        <div class="stat-card">
            <div class="stat-value">${stats.p95ResponseTime}ms</div>
            <div class="stat-label">P95 Response time</div>
        </div>
        <div class="stat-card">
            <div class="stat-value">${stats.errorRate}%</div>
            <div class="stat-label">Tỷ lệ lỗi</div>
        </div>
        <div class="stat-card">
            <div class="stat-value">${stats.rps}</div>
            <div class="stat-label">Requests/sec</div>
        </div>
    `;
}

function calculateStats(data) {
    if (data.length === 0) return {};
    
    const responseTimes = data.map(d => d.response_time_ms || 0);
    const successCount = data.filter(d => d.success || (d.status_code >= 200 && d.status_code < 300)).length;
    
    // Tính P95
    const sortedTimes = responseTimes.sort((a, b) => a - b);
    const p95Index = Math.floor(sortedTimes.length * 0.95);
    
    // Tính RPS
    const timestamps = data.map(d => new Date(d.timestamp).getTime());
    const duration = (Math.max(...timestamps) - Math.min(...timestamps)) / 1000;
    const rps = duration > 0 ? Math.round(data.length / duration) : 0;
    
    return {
        totalRequests: data.length,
        avgResponseTime: Math.round(responseTimes.reduce((a, b) => a + b, 0) / responseTimes.length),
        successRate: Math.round((successCount / data.length) * 100),
        errorRate: Math.round(((data.length - successCount) / data.length) * 100),
        p95ResponseTime: Math.round(sortedTimes[p95Index] || 0),
        rps: rps
    };
}

// Charts
function updateCharts() {
    destroyExistingCharts();
    createResponseTimeChart();
    createSuccessRateChart();
    createStatusCodeChart();
    createRpsChart();
}

function destroyExistingCharts() {
    Object.values(charts).forEach(chart => {
        if (chart) chart.destroy();
    });
    charts = {};
}

function createResponseTimeChart() {
    const ctx = document.getElementById('responseTimeChart').getContext('2d');
    const data = filteredData.map(d => ({
        x: new Date(d.timestamp),
        y: d.response_time_ms || 0
    }));
    
    charts.responseTime = new Chart(ctx, {
        type: 'line',
        data: {
            datasets: [{
                label: 'Response Time (ms)',
                data: data,
                borderColor: 'rgb(75, 192, 192)',
                backgroundColor: 'rgba(75, 192, 192, 0.1)',
                tension: 0.1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                x: {
                    type: 'time',
                    time: {
                        displayFormats: {
                            second: 'HH:mm:ss'
                        }
                    }
                },
                y: {
                    beginAtZero: true,
                    title: {
                        display: true,
                        text: 'Response Time (ms)'
                    }
                }
            }
        }
    });
}

function createSuccessRateChart() {
    const ctx = document.getElementById('successRateChart').getContext('2d');
    const successCount = filteredData.filter(d => d.success || (d.status_code >= 200 && d.status_code < 300)).length;
    const errorCount = filteredData.length - successCount;
    
    charts.successRate = new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: ['Thành công', 'Thất bại'],
            datasets: [{
                data: [successCount, errorCount],
                backgroundColor: ['#4CAF50', '#F44336'],
                borderWidth: 2
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });
}

function createStatusCodeChart() {
    const ctx = document.getElementById('statusCodeChart').getContext('2d');
    const statusCounts = {};
    
    filteredData.forEach(d => {
        const status = d.status_code || 'Unknown';
        statusCounts[status] = (statusCounts[status] || 0) + 1;
    });
    
    const labels = Object.keys(statusCounts).sort();
    const data = labels.map(label => statusCounts[label]);
    const colors = labels.map(status => {
        if (status >= 200 && status < 300) return '#4CAF50';
        if (status >= 300 && status < 400) return '#FF9800';
        if (status >= 400 && status < 500) return '#F44336';
        if (status >= 500) return '#9C27B0';
        return '#607D8B';
    });
    
    charts.statusCode = new Chart(ctx, {
        type: 'bar',
        data: {
            labels: labels,
            datasets: [{
                label: 'Số lượng',
                data: data,
                backgroundColor: colors,
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true,
                    title: {
                        display: true,
                        text: 'Số lượng requests'
                    }
                },
                x: {
                    title: {
                        display: true,
                        text: 'Status Code'
                    }
                }
            }
        }
    });
}

function createRpsChart() {
    const ctx = document.getElementById('rpsChart').getContext('2d');
    
    // Group by time intervals (10 second buckets)
    const buckets = {};
    filteredData.forEach(d => {
        const time = new Date(d.timestamp);
        const bucketTime = new Date(Math.floor(time.getTime() / 10000) * 10000);
        const key = bucketTime.toISOString();
        buckets[key] = (buckets[key] || 0) + 1;
    });
    
    const data = Object.keys(buckets).sort().map(key => ({
        x: new Date(key),
        y: buckets[key] / 10 // RPS for 10-second bucket
    }));
    
    charts.rps = new Chart(ctx, {
        type: 'line',
        data: {
            datasets: [{
                label: 'Requests per Second',
                data: data,
                borderColor: 'rgb(255, 99, 132)',
                backgroundColor: 'rgba(255, 99, 132, 0.1)',
                tension: 0.1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                x: {
                    type: 'time',
                    time: {
                        displayFormats: {
                            second: 'HH:mm:ss'
                        }
                    }
                },
                y: {
                    beginAtZero: true,
                    title: {
                        display: true,
                        text: 'Requests/Second'
                    }
                }
            }
        }
    });
}

// Filters
function applyFilters() {
    const statusFilter = document.getElementById('statusFilter').value;
    const timeFromFilter = document.getElementById('timeFromFilter').value;
    const timeToFilter = document.getElementById('timeToFilter').value;
    const responseTimeFilter = document.getElementById('responseTimeFilter').value;
    
    filteredData = rawData.filter(d => {
        // Status filter
        if (statusFilter === 'success' && (!d.success && !(d.status_code >= 200 && d.status_code < 300))) {
            return false;
        }
        if (statusFilter === 'error' && (d.success || (d.status_code >= 200 && d.status_code < 300))) {
            return false;
        }
        
        // Time filters
        const timestamp = new Date(d.timestamp);
        if (timeFromFilter && timestamp < new Date(timeFromFilter)) {
            return false;
        }
        if (timeToFilter && timestamp > new Date(timeToFilter)) {
            return false;
        }
        
        // Response time filter
        if (responseTimeFilter && d.response_time_ms > parseInt(responseTimeFilter)) {
            return false;
        }
        
        return true;
    });
    
    updateStats();
    updateCharts();
    showSuccess(`Đã áp dụng bộ lọc. Hiển thị ${filteredData.length}/${rawData.length} requests.`);
}

function resetFilters() {
    document.getElementById('statusFilter').value = 'all';
    document.getElementById('timeFromFilter').value = '';
    document.getElementById('timeToFilter').value = '';
    document.getElementById('responseTimeFilter').value = '';
    
    filteredData = [...rawData];
    updateStats();
    updateCharts();
    showSuccess('Đã reset bộ lọc.');
}

// Utility functions
function showError(message) {
    errorMessage.textContent = message;
    errorMessage.style.display = 'block';
    successMessage.style.display = 'none';
    setTimeout(() => {
        errorMessage.style.display = 'none';
    }, 5000);
}

function showSuccess(message) {
    successMessage.textContent = message;
    successMessage.style.display = 'block';
    errorMessage.style.display = 'none';
    setTimeout(() => {
        successMessage.style.display = 'none';
    }, 3000);
}
